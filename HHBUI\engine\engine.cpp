﻿#include "pch.h"
#include "engine.h"
#include "common/winapi.h"
#include "common/Exception.h"
#include <chrono>
#include <algorithm>
#include <memory>

namespace HHBUI
{
	// 静态成员变量定义
	std::atomic<bool> UIEngine::s_isInitialized{false};
	std::atomic<bool> UIEngine::s_isDebugMode{false};
	std::atomic<float> UIEngine::s_dpiScale{1.0f};
	std::mutex UIEngine::s_initMutex;

	namespace detail
	{
		/// <summary>
		/// 检测Windows版本是否为Windows 10或更高版本
		/// 使用现代C++异常安全的方式
		/// </summary>
		/// <returns>true表示Windows 10+，false表示较低版本</returns>
		[[nodiscard]] bool IsWindows10OrLater() noexcept
		{
			try
			{
				// 使用RAII管理库句柄
				struct LibraryHandle
				{
					HMODULE handle;
					explicit LibraryHandle(LPCWSTR name) : handle(LoadLibraryW(name)) {}
					~LibraryHandle() { if (handle) FreeLibrary(handle); }
					operator bool() const noexcept { return handle != nullptr; }
					operator HMODULE() const noexcept { return handle; }
				};

				LibraryHandle ntdll(L"ntdll.dll");
				if (!ntdll)
					return false;

				using RtlGetNtVersionNumbersProc = void(WINAPI*)(DWORD*, DWORD*, DWORD*);
				auto proc = reinterpret_cast<RtlGetNtVersionNumbersProc>(
					GetProcAddress(ntdll, "RtlGetNtVersionNumbers"));

				if (!proc)
					return false;

				DWORD majorVersion = 0, minorVersion = 0, buildNumber = 0;
				proc(&majorVersion, &minorVersion, &buildNumber);

				// Windows 10 = 10.0, Windows 11 = 10.0 (build >= 22000)
				return (majorVersion >= 10);
			}
			catch (...)
			{
				// 异常情况下假设为较低版本系统
				return false;
			}
		}
		/// <summary>
		/// 现代化的DPI管理器
		/// 使用RAII和异常安全的设计
		/// </summary>
		class DpiManager final
		{
		public:
			struct DpiInfo
			{
				float scaleX = 1.0f;
				float scaleY = 1.0f;
				float systemDpiX = USER_DEFAULT_SCREEN_DPI;
				float systemDpiY = USER_DEFAULT_SCREEN_DPI;
			};

			/// <summary>
			/// 计算DPI信息
			/// </summary>
			/// <param name="customScale">自定义缩放系数，0.0f表示使用系统DPI</param>
			/// <returns>DPI信息结构体</returns>
			[[nodiscard]] static DpiInfo CalculateDpiInfo(float customScale = 0.0f) noexcept
			{
				DpiInfo result;

				try
				{
					// 使用RAII管理DC
					struct DeviceContextHandle
					{
						HDC hdc;
						DeviceContextHandle() : hdc(::GetDC(nullptr)) {}
						~DeviceContextHandle() { if (hdc) ::ReleaseDC(nullptr, hdc); }
						operator HDC() const noexcept { return hdc; }
						operator bool() const noexcept { return hdc != nullptr; }
					};

					DeviceContextHandle dc;
					if (!dc)
						return result; // 返回默认值

					// 获取系统DPI
					result.systemDpiX = static_cast<float>(GetDeviceCaps(dc, LOGPIXELSX));
					result.systemDpiY = static_cast<float>(GetDeviceCaps(dc, LOGPIXELSY));

					// 计算缩放系数
					result.scaleX = result.systemDpiX / USER_DEFAULT_SCREEN_DPI;
					result.scaleY = result.systemDpiY / USER_DEFAULT_SCREEN_DPI;

					// 应用自定义缩放
					if (customScale > 0.0f)
					{
						result.scaleX = customScale;
						result.scaleY = customScale;
					}

					// 限制最大缩放系数，避免界面过大
					constexpr float maxScale = 2.0f;
					constexpr float fallbackScale = 1.25f;

					if (result.scaleX >= maxScale)
						result.scaleX = fallbackScale;
					if (result.scaleY >= maxScale)
						result.scaleY = fallbackScale;

					// 量化缩放系数到0.25的倍数，减少渲染误差
					constexpr float quantizeStep = 0.25f;
					result.scaleX = std::round(result.scaleX / quantizeStep) * quantizeStep;
					result.scaleY = std::round(result.scaleY / quantizeStep) * quantizeStep;

					// 更新全局DPI信息
					UIWinApi::ToList.CapsdpiX = result.systemDpiX;
					UIWinApi::ToList.CapsdpiY = result.systemDpiY;
					UIWinApi::ToList.drawing_default_dpi = result.scaleX;
				}
				catch (...)
				{
					// 异常情况下返回默认值
				}

				return result;
			}
		};
	} // namespace detail
	HRESULT UIEngine::Initialize(std::optional<EngineInitConfig> config) noexcept
	{
		// 线程安全的初始化检查
		std::lock_guard<std::mutex> lock(s_initMutex);

		if (s_isInitialized.load())
		{
			return S_FALSE; // 已经初始化
		}

		try
		{
			// 使用提供的配置或默认配置
			const EngineInitConfig actualConfig = config.value_or(EngineInitConfig{});

			return InitializeInternal(actualConfig);
		}
		catch (...)
		{
			// 确保异常安全，清理可能的部分初始化状态
			ShutdownInternal();
			return E_FAIL;
		}
	}

	HRESULT UIEngine::InitializeInternal(const EngineInitConfig& config) noexcept
	{
		try
		{
			// 1. COM初始化
			throw_if_failed(CoInitialize(nullptr), L"COM初始化失败");

			// 2. 设置DPI感知
			const bool isWin10Plus = detail::IsWindows10OrLater();
			UIWinApi::ToList.dwMajorVersion = isWin10Plus;

			if (isWin10Plus)
			{
				// Windows 10+ 支持更精确的DPI感知
				SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
			}
			else
			{
				// Windows 7/8 使用传统API
				SetProcessDPIAware();
			}

			// 3. DPI管理
			const auto dpiInfo = detail::DpiManager::CalculateDpiInfo(config.customDpiScale);
			s_dpiScale.store(dpiInfo.scaleX);

			// 4. 字体配置
			auto fontLogFont = std::make_unique<LOGFONTW>();
			SystemParametersInfoW(SPI_GETICONTITLELOGFONT, sizeof(LOGFONTW), fontLogFont.get(), FALSE);

			// 应用自定义字体配置
			if (!config.defaultFont.fontFace.empty())
			{
				const size_t faceNameLength = std::min(config.defaultFont.fontFace.length(),
					static_cast<size_t>(LF_FACESIZE - 1));
				std::wmemcpy(fontLogFont->lfFaceName, config.defaultFont.fontFace.data(), faceNameLength);
				fontLogFont->lfFaceName[faceNameLength] = L'\0';
			}

			fontLogFont->lfHeight = -static_cast<LONG>(ScaleByDpi(static_cast<float>(config.defaultFont.fontSize)));

			// 转移字体所有权到全局状态
			UIWinApi::ToList.drawing_default_fontLogFont = fontLogFont.release();

			// 5. 应用程序实例配置
			HINSTANCE appInstance = config.applicationInstance;
			if (!appInstance)
			{
				appInstance = GetModuleHandleW(nullptr);
			}

			// 6. 初始化子系统
			handle_if_failed(UIWinApi::Init(appInstance), L"WinAPI初始化失败", {});
			handle_if_failed(UIDrawContext::Init(config.renderDevice), L"绘图上下文初始化失败", {});

			// 7. 创建默认字体对象
			UIWinApi::ToList.default_font = new UIFont();

			// 8. 提取应用程序图标
			if (appInstance)
			{
				std::array<TCHAR, MAX_PATH + 1> filePath{};
				GetModuleFileName(appInstance, filePath.data(), MAX_PATH);

				UIWinApi::ToList.hIcon = ExtractIconW(appInstance, filePath.data(), 0);
				UIWinApi::ToList.hIconsm = ExtractIconW(appInstance, filePath.data(), 0);
				UIWinApi::ToList.engine_instance = appInstance;
			}

			// 9. 注册窗口类
			UIWnd::RegClass(L"Hhbui.WindowClass.UI", 0, 0);

			// 10. 设置全局状态
			s_isDebugMode.store(config.enableDebugMode);
			UIWinApi::ToList.dwDebug = config.enableDebugMode;

			// 标记初始化完成
			s_isInitialized.store(true);

			return S_OK;
		}
		catch_default({});
	}
	HRESULT UIEngine::Shutdown() noexcept
	{
		std::lock_guard<std::mutex> lock(s_initMutex);

		if (!s_isInitialized.load())
		{
			return S_FALSE; // 未初始化
		}

		try
		{
			ShutdownInternal();
			s_isInitialized.store(false);
			return S_OK;
		}
		catch (...)
		{
			// 即使出现异常也要标记为未初始化状态
			s_isInitialized.store(false);
			return E_FAIL;
		}
	}

	void UIEngine::ShutdownInternal() noexcept
	{
		try
		{
			// 按照初始化的逆序进行清理

			// 1. 清理字体资源
			if (UIWinApi::ToList.default_font)
			{
				delete UIWinApi::ToList.default_font;
				UIWinApi::ToList.default_font = nullptr;
			}

			if (UIWinApi::ToList.drawing_default_fontLogFont)
			{
				delete UIWinApi::ToList.drawing_default_fontLogFont;
				UIWinApi::ToList.drawing_default_fontLogFont = nullptr;
			}

			// 2. 清理图标资源
			if (UIWinApi::ToList.hIcon)
			{
				DestroyIcon(UIWinApi::ToList.hIcon);
				UIWinApi::ToList.hIcon = nullptr;
			}

			if (UIWinApi::ToList.hIconsm)
			{
				DestroyIcon(UIWinApi::ToList.hIconsm);
				UIWinApi::ToList.hIconsm = nullptr;
			}

			// 3. 清理子系统
			UIDrawContext::UnInit();
			UIWinApi::UnInit();

			// 4. COM反初始化
			CoUninitialize();

			// 5. 重置全局状态
			UIWinApi::ToList.engine_instance = nullptr;
			s_isDebugMode.store(false);
			s_dpiScale.store(1.0f);
		}
		catch (...)
		{
			// 静默处理清理过程中的异常
			// 确保程序能够正常退出
		}
	}

	bool UIEngine::IsDebugMode() noexcept
	{
		return s_isDebugMode.load();
	}

	bool UIEngine::IsInitialized() noexcept
	{
		return s_isInitialized.load();
	}

	float UIEngine::ScaleByDpi(float value) noexcept
	{
		const float scale = s_dpiScale.load();
		if (scale > 1.0f)
		{
			return std::round(value * scale);
		}
		return value;
	}

	float UIEngine::GetDpiScale() noexcept
	{
		return s_dpiScale.load();
	}

	double UIEngine::GetElapsedTime() noexcept
	{
		// 使用高精度时钟和线程安全的静态变量
		static const auto startTime = std::chrono::high_resolution_clock::now();

		const auto currentTime = std::chrono::high_resolution_clock::now();
		const auto elapsed = std::chrono::duration_cast<std::chrono::duration<double>>(
			currentTime - startTime);

		return elapsed.count();
	}

	std::wstring_view UIEngine::GetVersion() noexcept
	{
		return HHBUI_VERSION;
	}

	UIEngine::EngineStatus UIEngine::GetStatus() noexcept
	{
		EngineStatus status;
		status.isInitialized = s_isInitialized.load();
		status.isDebugMode = s_isDebugMode.load();
		status.dpiScale = s_dpiScale.load();
		status.elapsedTime = GetElapsedTime();
		status.version = std::wstring(GetVersion());
		return status;
	}

	// 为了向后兼容，保留旧的函数名（标记为废弃）
	[[deprecated("Use UIEngine::Initialize() instead")]]
	HRESULT HHBUI::UIEngine::Init(info_Init* info)
	{
		if (info)
		{
			EngineInitConfig config;
			config.renderDevice = info->device;
			config.applicationInstance = info->hInstance;
			config.customDpiScale = info->dwScaledpi;
			config.enableDebugMode = info->dwDebug != FALSE;
			config.defaultFont.fontFace = info->default_font_Face ? info->default_font_Face : L"";
			config.defaultFont.fontSize = info->default_font_Size;
			config.defaultFont.fontStyle = info->default_font_Style;

			return Initialize(config);
		}
		else
		{
			return Initialize(std::nullopt);
		}
	}

	[[deprecated("Use UIEngine::Shutdown() instead")]]
	HRESULT HHBUI::UIEngine::UnInit()
	{
		return Shutdown();
	}

	[[deprecated("Use UIEngine::IsDebugMode() instead")]]
	BOOL HHBUI::UIEngine::QueryDebug()
	{
		return IsDebugMode() ? TRUE : FALSE;
	}

	[[deprecated("Use UIEngine::IsInitialized() instead")]]
	BOOL HHBUI::UIEngine::QueryInit()
	{
		return IsInitialized() ? TRUE : FALSE;
	}

	[[deprecated("Use UIEngine::ScaleByDpi() instead")]]
	FLOAT HHBUI::UIEngine::fScale(FLOAT n)
	{
		return ScaleByDpi(n);
	}

	[[deprecated("Use UIEngine::GetDpiScale() instead")]]
	FLOAT HHBUI::UIEngine::GetDefaultScale()
	{
		return GetDpiScale();
	}

	[[deprecated("Use UIEngine::GetElapsedTime() instead")]]
	FLOAT HHBUI::UIEngine::GetTime()
	{
		return static_cast<FLOAT>(GetElapsedTime());
	}

	[[deprecated("Use UIEngine::GetVersion() instead")]]
	LPCWSTR HHBUI::UIEngine::GetVersion()
	{
		static const std::wstring version(GetVersion());
		return version.c_str();
	}

} // namespace HHBUI