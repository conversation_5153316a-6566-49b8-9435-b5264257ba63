#pragma once
#include <thread>
#include <condition_variable>
#include <map>
#include <unordered_map>
#include <queue>
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>
#include <functional>
#include <optional>
#include <vector>
#include <list>

namespace HHBUI
{
	/// <summary>
	/// 现代化的渲染线程基类
	/// 使用RAII设计模式，确保线程安全和资源管理
	/// 支持暂停/恢复、优雅关闭等功能
	/// </summary>
	class TOAPI UIRenderThread
	{
	public:
		/// <summary>
		/// 线程状态枚举
		/// </summary>
		enum class ThreadState : int
		{
			Stopped = 0,    // 已停止
			Running = 1,    // 运行中
			Paused = 2,     // 已暂停
			Stopping = 3    // 正在停止
		};

		UIRenderThread() = default;
		virtual ~UIRenderThread();

		// 禁用拷贝，允许移动
		UIRenderThread(const UIRenderThread&) = delete;
		UIRenderThread& operator=(const UIRenderThread&) = delete;
		UIRenderThread(UIRenderThread&&) = default;
		UIRenderThread& operator=(UIRenderThread&&) = default;

		/// <summary>
		/// 启动渲染线程
		/// </summary>
		/// <param name="startPaused">是否以暂停状态启动</param>
		/// <returns>true表示成功启动，false表示已经在运行</returns>
		[[nodiscard]] virtual bool Start(bool startPaused = false) noexcept;

		/// <summary>
		/// 暂停线程执行
		/// </summary>
		virtual void Pause() noexcept;

		/// <summary>
		/// 恢复线程执行
		/// </summary>
		virtual void Resume() noexcept;

		/// <summary>
		/// 停止线程（阻塞等待线程结束）
		/// </summary>
		/// <param name="timeoutMs">超时时间（毫秒），0表示无限等待</param>
		/// <returns>true表示成功停止，false表示超时</returns>
		[[nodiscard]] virtual bool Stop(std::chrono::milliseconds timeoutMs = std::chrono::milliseconds{0}) noexcept;

		/// <summary>
		/// 获取当前线程状态
		/// </summary>
		/// <returns>线程状态</returns>
		[[nodiscard]] ThreadState GetState() const noexcept;

		/// <summary>
		/// 检查线程是否正在运行
		/// </summary>
		/// <returns>true表示正在运行</returns>
		[[nodiscard]] bool IsRunning() const noexcept;

		/// <summary>
		/// 检查线程是否已暂停
		/// </summary>
		/// <returns>true表示已暂停</returns>
		[[nodiscard]] bool IsPaused() const noexcept;

	protected:
		/// <summary>
		/// 渲染线程主循环函数（子类必须实现）
		/// 注意：此函数应该定期检查是否需要暂停或停止
		/// </summary>
		virtual void RenderLoop() = 0;

		/// <summary>
		/// 检查是否应该继续运行
		/// 子类可以在RenderLoop中调用此函数来检查状态
		/// </summary>
		/// <returns>true表示应该继续运行</returns>
		[[nodiscard]] bool ShouldContinue() const noexcept;

		/// <summary>
		/// 等待暂停状态结束
		/// 子类可以在适当的位置调用此函数来响应暂停请求
		/// </summary>
		void WaitIfPaused() noexcept;

	private:
		void ThreadMain() noexcept;

		mutable std::mutex m_stateMutex;
		std::condition_variable m_stateCondition;
		std::unique_ptr<std::thread> m_thread;
		std::atomic<ThreadState> m_state{ThreadState::Stopped};
	};
	/// <summary>
	/// 高精度FPS计数器和帧率限制器
	/// 线程安全，支持动态FPS限制和精确的帧率统计
	/// </summary>
	class TOAPI UIFPSCounter final
	{
	public:
		UIFPSCounter() = default;
		~UIFPSCounter() = default;

		// 禁用拷贝，允许移动
		UIFPSCounter(const UIFPSCounter&) = delete;
		UIFPSCounter& operator=(const UIFPSCounter&) = delete;
		UIFPSCounter(UIFPSCounter&&) = default;
		UIFPSCounter& operator=(UIFPSCounter&&) = default;

		/// <summary>
		/// 计算当前FPS
		/// 使用滑动窗口算法，提供更平滑的FPS值
		/// </summary>
		/// <returns>当前FPS值</returns>
		[[nodiscard]] float CalculateFPS() noexcept;

		/// <summary>
		/// 设置最大FPS限制
		/// </summary>
		/// <param name="maxFps">最大FPS值，负值表示无限制</param>
		void SetMaxFPS(float maxFps) noexcept;

		/// <summary>
		/// 获取当前FPS限制
		/// </summary>
		/// <returns>FPS限制值，负值表示无限制</returns>
		[[nodiscard]] float GetMaxFPS() const noexcept;

		/// <summary>
		/// 执行FPS限制（阻塞到下一帧时间）
		/// 应该在每帧结束时调用
		/// </summary>
		void LimitFPS() noexcept;

		/// <summary>
		/// 重置FPS统计
		/// </summary>
		void Reset() noexcept;

		/// <summary>
		/// 获取详细的FPS统计信息
		/// </summary>
		struct FPSStats
		{
			float currentFPS = 0.0f;        // 当前FPS
			float averageFPS = 0.0f;        // 平均FPS
			float minFPS = 0.0f;            // 最小FPS
			float maxFPS = 0.0f;            // 最大FPS
			std::uint64_t totalFrames = 0;  // 总帧数
			double elapsedTime = 0.0;       // 总运行时间（秒）
		};
		[[nodiscard]] FPSStats GetStats() const noexcept;

	private:
		mutable std::mutex m_mutex;

		// FPS计算相关
		float m_currentFPS = 0.0f;
		std::uint32_t m_frameCount = 0;
		std::chrono::high_resolution_clock::time_point m_lastUpdateTime = std::chrono::high_resolution_clock::now();
		std::chrono::high_resolution_clock::time_point m_startTime = std::chrono::high_resolution_clock::now();

		// FPS限制相关
		float m_maxFPS = -1.0f; // 负值表示无限制
		std::chrono::high_resolution_clock::time_point m_lastFrameTime = std::chrono::high_resolution_clock::now();
		std::chrono::nanoseconds m_frameDuration{0};

		// 统计信息
		float m_minFPS = std::numeric_limits<float>::max();
		float m_maxFPSRecorded = 0.0f;
		std::uint64_t m_totalFrames = 0;

		// 滑动窗口用于平滑FPS计算
		static constexpr size_t SAMPLE_COUNT = 60;
		std::array<float, SAMPLE_COUNT> m_fpsSamples{};
		size_t m_sampleIndex = 0;
		bool m_samplesValid = false;
	};
	/// <summary>
	/// 线程安全的高性能队列
	/// 支持多生产者多消费者模式，使用现代C++17特性
	/// </summary>
	template <typename T>
	class TOAPI UIQueue final
	{
	public:
		UIQueue() = default;
		~UIQueue() = default;

		// 禁用拷贝，允许移动
		UIQueue(const UIQueue&) = delete;
		UIQueue& operator=(const UIQueue&) = delete;
		UIQueue(UIQueue&&) = default;
		UIQueue& operator=(UIQueue&&) = default;

		/// <summary>
		/// 检查队列是否为空
		/// </summary>
		/// <returns>true表示队列为空</returns>
		[[nodiscard]] bool empty() const noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			return m_queue.empty();
		}

		/// <summary>
		/// 获取队列大小
		/// </summary>
		/// <returns>队列中元素的数量</returns>
		[[nodiscard]] size_t size() const noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			return m_queue.size();
		}

		/// <summary>
		/// 在队列前端插入元素（高优先级）
		/// </summary>
		/// <param name="item">要插入的元素</param>
		void push_front(const T& item)
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_queue.push_front(item);
		}

		/// <summary>
		/// 在队列前端插入元素（移动语义）
		/// </summary>
		/// <param name="item">要插入的元素</param>
		void push_front(T&& item)
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_queue.push_front(std::move(item));
		}

		/// <summary>
		/// 在队列后端插入元素
		/// </summary>
		/// <param name="item">要插入的元素</param>
		void push_back(const T& item)
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_queue.push_back(item);
		}

		/// <summary>
		/// 在队列后端插入元素（移动语义）
		/// </summary>
		/// <param name="item">要插入的元素</param>
		void push_back(T&& item)
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_queue.push_back(std::move(item));
		}

		/// <summary>
		/// 就地构造元素并插入队列后端
		/// </summary>
		/// <param name="args">构造参数</param>
		template<typename... Args>
		void emplace_back(Args&&... args)
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_queue.emplace_back(std::forward<Args>(args)...);
		}

		/// <summary>
		/// 从队列前端取出元素
		/// </summary>
		/// <returns>元素的optional包装，如果队列为空则返回nullopt</returns>
		[[nodiscard]] std::optional<T> pop_front() noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			if (m_queue.empty())
				return std::nullopt;

			T item = std::move(m_queue.front());
			m_queue.pop_front();
			return item;
		}

		/// <summary>
		/// 从队列前端取出元素（传统接口）
		/// </summary>
		/// <param name="item">输出参数，存储取出的元素</param>
		/// <returns>true表示成功取出元素，false表示队列为空</returns>
		[[nodiscard]] bool try_pop_front(T& item) noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			if (m_queue.empty())
				return false;

			item = std::move(m_queue.front());
			m_queue.pop_front();
			return true;
		}

		/// <summary>
		/// 清空队列
		/// </summary>
		void clear() noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_queue.clear();
		}

		/// <summary>
		/// 获取队列的副本（用于调试）
		/// 注意：这是一个昂贵的操作，仅用于调试目的
		/// </summary>
		/// <returns>队列内容的副本</returns>
		[[nodiscard]] std::vector<T> snapshot() const
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			return std::vector<T>(m_queue.begin(), m_queue.end());
		}

		// 为了向后兼容，保留旧的方法名（标记为废弃）
		[[deprecated("Use push_front() instead")]]
		void insertquque(const T& t) { push_front(t); }

		[[deprecated("Use push_back() instead")]]
		void enqueue(const T& t) { push_back(t); }

		[[deprecated("Use try_pop_front() instead")]]
		bool dequeue(T& t) { return try_pop_front(t); }

	private:
		mutable std::mutex m_mutex;
		std::list<T> m_queue;
	};
	/// <summary>
	/// UI对象基类
	/// 提供基础的UI对象功能和生命周期管理
	/// 使用现代C++设计模式，确保类型安全和资源管理
	/// </summary>
	class TOAPI UIBase
	{
	public:
		UIBase() = default;
		virtual ~UIBase() = default;

		// 禁用拷贝，允许移动
		UIBase(const UIBase&) = delete;
		UIBase& operator=(const UIBase&) = delete;
		UIBase(UIBase&&) = default;
		UIBase& operator=(UIBase&&) = default;

		/// <summary>
		/// 获取关联的视图对象
		/// </summary>
		/// <returns>视图对象指针</returns>
		[[nodiscard]] void* GetView() const noexcept { return m_view; }

		/// <summary>
		/// 设置关联的视图对象
		/// </summary>
		/// <param name="view">视图对象指针</param>
		void SetView(void* view) noexcept { m_view = view; }

		/// <summary>
		/// 获取关联的窗口对象
		/// </summary>
		/// <returns>窗口对象指针</returns>
		[[nodiscard]] void* GetWindow() const noexcept { return m_window; }

		/// <summary>
		/// 设置关联的窗口对象
		/// </summary>
		/// <param name="window">窗口对象指针</param>
		void SetWindow(void* window) noexcept { m_window = window; }

		/// <summary>
		/// 获取第一个子对象
		/// </summary>
		/// <returns>子对象指针</returns>
		[[nodiscard]] void* GetFirstChild() const noexcept { return m_firstChild; }

		/// <summary>
		/// 获取最后一个子对象
		/// </summary>
		/// <returns>子对象指针</returns>
		[[nodiscard]] void* GetLastChild() const noexcept { return m_lastChild; }

	protected:
		void* m_view = nullptr;        // 关联的视图对象
		void* m_window = nullptr;      // 关联的窗口对象
		void* m_firstChild = nullptr;  // 第一个子对象
		void* m_lastChild = nullptr;   // 最后一个子对象

		// 友元类声明
		friend class UIWnd;
		friend class UIControl;
		friend class UICanvas;
		friend class UILayout;
		friend class UIAnimation;

		// 为了向后兼容，保留旧的成员变量名（标记为废弃）
		[[deprecated("Use GetView()/SetView() instead")]]
		void*& m_UIView = m_view;

		[[deprecated("Use GetWindow()/SetWindow() instead")]]
		void*& m_UIWindow = m_window;

		[[deprecated("Use GetFirstChild() instead")]]
		void*& m_objChildFirst = m_firstChild;

		[[deprecated("Use GetLastChild() instead")]]
		void*& m_objChildLast = m_lastChild;
	};

	/// <summary>
	/// 定时器信息结构体
	/// 使用现代C++特性，提供类型安全和默认值
	/// </summary>
	struct TimerInfo final
	{
		UIBase* targetObject = nullptr;     // 目标UI对象
		size_t localId = 0;                 // 本地ID
		HWND windowHandle = nullptr;        // 窗口句柄
		UINT systemTimerId = 0;             // 系统定时器ID
		bool isDestroyed = false;           // 是否已销毁

		// 默认构造函数
		TimerInfo() = default;

		// 便利构造函数
		TimerInfo(UIBase* obj, size_t id, HWND hwnd, UINT timerId) noexcept
			: targetObject(obj), localId(id), windowHandle(hwnd), systemTimerId(timerId) {}

		// 禁用拷贝，允许移动
		TimerInfo(const TimerInfo&) = delete;
		TimerInfo& operator=(const TimerInfo&) = delete;
		TimerInfo(TimerInfo&&) = default;
		TimerInfo& operator=(TimerInfo&&) = default;

		// 为了向后兼容，保留旧的成员变量名（标记为废弃）
		[[deprecated("Use targetObject instead")]]
		UIBase*& pPropObj = targetObject;

		[[deprecated("Use localId instead")]]
		size_t& nLocalID = localId;

		[[deprecated("Use windowHandle instead")]]
		HWND& hWnd = windowHandle;

		[[deprecated("Use systemTimerId instead")]]
		UINT& uWinTimer = systemTimerId;

		[[deprecated("Use isDestroyed instead")]]
		bool& bKilled = isDestroyed;
	};

	/// <summary>
	/// 定时器信息容器类型
	/// 使用现代容器，提供更好的性能和类型安全
	/// </summary>
	using TimerInfoVector = std::vector<TimerInfo>;

	// 为了向后兼容，保留旧的类型名（标记为废弃）
	[[deprecated("Use TimerInfo instead")]]
	using tagTimerInfo = TimerInfo;

	[[deprecated("Use TimerInfoVector instead")]]
	using VecTimerInfo = TimerInfoVector;

} // namespace HHBUI