﻿#pragma once
#include <memory>
#include <optional>
#include <string_view>
#include <atomic>
#include <mutex>

namespace HHBUI
{
	/// <summary>
	/// 引擎初始化配置结构体
	/// 使用现代C++17特性，提供默认值和类型安全
	/// </summary>
	struct EngineInitConfig final
	{
		// 渲染设备索引 (-1 = 自动选择最佳设备)
		int renderDevice = -1;

		// 应用程序实例句柄 (nullptr = 使用当前模块)
		HINSTANCE applicationInstance = nullptr;

		// 自定义DPI缩放系数 (0.0f = 使用系统DPI)
		float customDpiScale = 0.0f;

		// 是否启用调试模式
		bool enableDebugMode = false;

		// 默认字体配置
		struct FontConfig
		{
			std::wstring_view fontFace = L""; // 空字符串使用系统默认字体
			int fontSize = 14;
			DWORD fontStyle = 0; // 参考 FontStyle 枚举
		} defaultFont;

		// 构造函数提供合理默认值
		EngineInitConfig() = default;

		// 禁用拷贝，允许移动
		EngineInitConfig(const EngineInitConfig&) = delete;
		EngineInitConfig& operator=(const EngineInitConfig&) = delete;
		EngineInitConfig(EngineInitConfig&&) = default;
		EngineInitConfig& operator=(EngineInitConfig&&) = default;
	};

	/// <summary>
	/// HHBUI引擎核心类
	/// 采用RAII设计模式，确保资源安全管理
	/// 线程安全的单例模式实现
	/// </summary>
	class TOAPI UIEngine final
	{
	public:
		// 禁用拷贝和移动构造
		UIEngine() = delete;
		UIEngine(const UIEngine&) = delete;
		UIEngine& operator=(const UIEngine&) = delete;
		UIEngine(UIEngine&&) = delete;
		UIEngine& operator=(UIEngine&&) = delete;

		/// <summary>
		/// 引擎初始化
		/// </summary>
		/// <param name="config">初始化配置，使用std::optional提供类型安全</param>
		/// <returns>执行状态</returns>
		[[nodiscard]] static HRESULT Initialize(std::optional<EngineInitConfig> config = std::nullopt) noexcept;

		/// <summary>
		/// 引擎反初始化
		/// 自动清理所有资源，异常安全
		/// </summary>
		/// <returns>执行状态</returns>
		[[nodiscard]] static HRESULT Shutdown() noexcept;

		/// <summary>
		/// 查询引擎是否处于调试模式
		/// </summary>
		/// <returns>调试模式状态</returns>
		[[nodiscard]] static bool IsDebugMode() noexcept;

		/// <summary>
		/// 查询引擎是否已初始化
		/// </summary>
		/// <returns>初始化状态</returns>
		[[nodiscard]] static bool IsInitialized() noexcept;

		/// <summary>
		/// 计算DPI缩放后的值
		/// </summary>
		/// <param name="value">原始值</param>
		/// <returns>缩放后的值</returns>
		[[nodiscard]] static float ScaleByDpi(float value) noexcept;

		/// <summary>
		/// 获取当前DPI缩放系数
		/// </summary>
		/// <returns>DPI缩放系数</returns>
		[[nodiscard]] static float GetDpiScale() noexcept;

		/// <summary>
		/// 获取引擎运行时间（秒）
		/// 高精度时间计算，适用于动画和性能分析
		/// </summary>
		/// <returns>运行时间</returns>
		[[nodiscard]] static double GetElapsedTime() noexcept;

		/// <summary>
		/// 获取引擎版本信息
		/// </summary>
		/// <returns>版本字符串</returns>
		[[nodiscard]] static std::wstring_view GetVersion() noexcept;

		/// <summary>
		/// 获取引擎状态信息（调试用）
		/// </summary>
		struct EngineStatus
		{
			bool isInitialized = false;
			bool isDebugMode = false;
			float dpiScale = 1.0f;
			double elapsedTime = 0.0;
			std::wstring version;
		};
		[[nodiscard]] static EngineStatus GetStatus() noexcept;

	private:
		// 线程安全的状态管理
		static std::atomic<bool> s_isInitialized;
		static std::atomic<bool> s_isDebugMode;
		static std::atomic<float> s_dpiScale;
		static std::mutex s_initMutex;

		// 内部初始化辅助函数
		static HRESULT InitializeInternal(const EngineInitConfig& config) noexcept;
		static void ShutdownInternal() noexcept;
	};

	// 为了向后兼容，保留旧的结构体名称（标记为废弃）
	[[deprecated("Use EngineInitConfig instead")]]
	using info_Init = EngineInitConfig;
}
