﻿#include "pch.h"
#include "base.h"
#include <chrono>
#include <thread>
#include <algorithm>
#include <numeric>

namespace HHBUI
{
	// UIFPSCounter 实现
	float UIFPSCounter::CalculateFPS() noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		const auto currentTime = std::chrono::high_resolution_clock::now();
		const auto deltaTime = std::chrono::duration_cast<std::chrono::microseconds>(
			currentTime - m_lastUpdateTime);

		++m_frameCount;
		++m_totalFrames;

		// 每秒更新一次FPS计算
		constexpr auto updateInterval = std::chrono::seconds(1);
		if (deltaTime >= updateInterval)
		{
			// 计算当前FPS
			const double deltaSeconds = deltaTime.count() / 1000000.0;
			m_currentFPS = static_cast<float>(m_frameCount / deltaSeconds);

			// 更新统计信息
			if (m_currentFPS > 0.0f)
			{
				m_minFPS = std::min(m_minFPS, m_currentFPS);
				m_maxFPSRecorded = std::max(m_maxFPSRecorded, m_currentFPS);

				// 更新滑动窗口
				m_fpsSamples[m_sampleIndex] = m_currentFPS;
				m_sampleIndex = (m_sampleIndex + 1) % SAMPLE_COUNT;
				if (m_sampleIndex == 0)
					m_samplesValid = true;
			}

			// 重置计数器
			m_frameCount = 0;
			m_lastUpdateTime = currentTime;
		}

		return m_currentFPS;
	}
	void UIFPSCounter::SetMaxFPS(float maxFps) noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		m_maxFPS = maxFps;

		if (maxFps > 0.0f)
		{
			// 计算每帧的时间间隔
			const double frameTimeSeconds = 1.0 / maxFps;
			m_frameDuration = std::chrono::duration_cast<std::chrono::nanoseconds>(
				std::chrono::duration<double>(frameTimeSeconds));
		}
		else
		{
			// 负值表示无限制
			m_frameDuration = std::chrono::nanoseconds{0};
		}
	}

	float UIFPSCounter::GetMaxFPS() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		return m_maxFPS;
	}

	void UIFPSCounter::LimitFPS() noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		if (m_maxFPS <= 0.0f || m_frameDuration.count() == 0)
		{
			// 无FPS限制
			m_lastFrameTime = std::chrono::high_resolution_clock::now();
			return;
		}

		const auto currentTime = std::chrono::high_resolution_clock::now();
		const auto targetTime = m_lastFrameTime + m_frameDuration;

		if (currentTime < targetTime)
		{
			// 需要等待到目标时间
			const auto sleepDuration = targetTime - currentTime;

			// 释放锁，避免在睡眠期间阻塞其他操作
			lock.~lock_guard();

			// 使用高精度睡眠
			std::this_thread::sleep_for(sleepDuration);

			// 重新获取锁并更新时间
			std::lock_guard<std::mutex> newLock(m_mutex);
			m_lastFrameTime = targetTime;
		}
		else
		{
			// 已经超过目标时间，直接更新
			m_lastFrameTime = currentTime;
		}
	}

	void UIFPSCounter::Reset() noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		const auto now = std::chrono::high_resolution_clock::now();

		m_currentFPS = 0.0f;
		m_frameCount = 0;
		m_totalFrames = 0;
		m_lastUpdateTime = now;
		m_lastFrameTime = now;
		m_startTime = now;

		m_minFPS = std::numeric_limits<float>::max();
		m_maxFPSRecorded = 0.0f;

		m_fpsSamples.fill(0.0f);
		m_sampleIndex = 0;
		m_samplesValid = false;
	}

	UIFPSCounter::FPSStats UIFPSCounter::GetStats() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		FPSStats stats;
		stats.currentFPS = m_currentFPS;
		stats.totalFrames = m_totalFrames;

		const auto elapsedTime = std::chrono::duration_cast<std::chrono::duration<double>>(
			std::chrono::high_resolution_clock::now() - m_startTime);
		stats.elapsedTime = elapsedTime.count();

		// 计算平均FPS
		if (stats.elapsedTime > 0.0)
		{
			stats.averageFPS = static_cast<float>(stats.totalFrames / stats.elapsedTime);
		}

		// 设置最小/最大FPS
		stats.minFPS = (m_minFPS == std::numeric_limits<float>::max()) ? 0.0f : m_minFPS;
		stats.maxFPS = m_maxFPSRecorded;

		return stats;
	}

	// UIRenderThread 实现
	UIRenderThread::~UIRenderThread()
	{
		// 确保线程在析构时正确停止
		Stop();
	}

	bool UIRenderThread::Start(bool startPaused) noexcept
	{
		std::lock_guard<std::mutex> lock(m_stateMutex);

		if (m_state.load() != ThreadState::Stopped)
		{
			return false; // 线程已经在运行
		}

		try
		{
			// 设置初始状态
			m_state.store(startPaused ? ThreadState::Paused : ThreadState::Running);

			// 创建线程
			m_thread = std::make_unique<std::thread>(&UIRenderThread::ThreadMain, this);

			return true;
		}
		catch (...)
		{
			// 线程创建失败，重置状态
			m_state.store(ThreadState::Stopped);
			return false;
		}
	}

	void UIRenderThread::Pause() noexcept
	{
		const auto currentState = m_state.load();
		if (currentState == ThreadState::Running)
		{
			m_state.store(ThreadState::Paused);
		}
	}

	void UIRenderThread::Resume() noexcept
	{
		{
			std::lock_guard<std::mutex> lock(m_stateMutex);
			const auto currentState = m_state.load();
			if (currentState == ThreadState::Paused)
			{
				m_state.store(ThreadState::Running);
			}
		}
		m_stateCondition.notify_one();
	}

	bool UIRenderThread::Stop(std::chrono::milliseconds timeoutMs) noexcept
	{
		{
			std::lock_guard<std::mutex> lock(m_stateMutex);
			const auto currentState = m_state.load();
			if (currentState == ThreadState::Stopped)
			{
				return true; // 已经停止
			}

			// 设置停止状态
			m_state.store(ThreadState::Stopping);
		}

		// 通知线程退出
		m_stateCondition.notify_all();

		if (m_thread && m_thread->joinable())
		{
			try
			{
				if (timeoutMs.count() > 0)
				{
					// 有超时限制的等待
					auto future = std::async(std::launch::async, [this]() {
						m_thread->join();
					});

					if (future.wait_for(timeoutMs) == std::future_status::timeout)
					{
						// 超时，强制分离线程
						m_thread->detach();
						m_thread.reset();
						m_state.store(ThreadState::Stopped);
						return false;
					}
				}
				else
				{
					// 无限等待
					m_thread->join();
				}

				m_thread.reset();
				m_state.store(ThreadState::Stopped);
				return true;
			}
			catch (...)
			{
				// 异常情况下强制重置
				m_thread.reset();
				m_state.store(ThreadState::Stopped);
				return false;
			}
		}

		m_state.store(ThreadState::Stopped);
		return true;
	}

	UIRenderThread::ThreadState UIRenderThread::GetState() const noexcept
	{
		return m_state.load();
	}

	bool UIRenderThread::IsRunning() const noexcept
	{
		return m_state.load() == ThreadState::Running;
	}

	bool UIRenderThread::IsPaused() const noexcept
	{
		return m_state.load() == ThreadState::Paused;
	}

	bool UIRenderThread::ShouldContinue() const noexcept
	{
		const auto state = m_state.load();
		return state == ThreadState::Running || state == ThreadState::Paused;
	}

	void UIRenderThread::WaitIfPaused() noexcept
	{
		std::unique_lock<std::mutex> lock(m_stateMutex);
		m_stateCondition.wait(lock, [this]() {
			const auto state = m_state.load();
			return state != ThreadState::Paused;
		});
	}

	void UIRenderThread::ThreadMain() noexcept
	{
		try
		{
			while (ShouldContinue())
			{
				// 检查是否需要暂停
				if (m_state.load() == ThreadState::Paused)
				{
					WaitIfPaused();
					continue;
				}

				// 执行渲染循环
				RenderLoop();
			}
		}
		catch (...)
		{
			// 捕获渲染循环中的异常，确保线程能够正常退出
		}

		// 线程即将退出，确保状态正确
		m_state.store(ThreadState::Stopped);
	}

} // namespace HHBUI
